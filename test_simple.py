#!/usr/bin/env python3
"""简化版原始项目测试脚本 - 修复字符问题"""
import requests
import json
import time
import sys

def test_transcript_api():
    """测试原始项目API"""
    url = "http://localhost:2024/transcript"
    
    # 测试数据 - 使用简单的ASCII字符
    data = {
        "messages": [
            {
                "type": "human",
                "content": {
                    "course_basic_info": "小学三年级数学，面积单元",
                    "teaching_info": "让学生理解面积概念，学会计算长方形面积",
                    "personal_requirements": "注重动手操作，多用生活实例"
                }
            }
        ]
    }
    
    print("发送请求...")
    start_time = time.time()
    
    try:
        response = requests.post(url, json=data, timeout=300)
        response.raise_for_status()
        
        result = response.json()
        end_time = time.time()
        
        # 分析结果
        final_transcript = result.get('final_transcript', '')
        teaching_processes = result.get('teaching_processes', {})
        
        print(f"请求成功，耗时: {end_time - start_time:.2f}秒")
        print(f"结果统计:")
        print(f"- 最终逐字稿长度: {len(final_transcript)}字")
        print(f"- 教学环节数量: {len(teaching_processes)}")
        
        # 显示教学环节
        if teaching_processes:
            print(f"教学环节:")
            for process_id, process_info in teaching_processes.items():
                title = process_info.get('process_title', '未知')
                transcript_len = len(process_info.get('transcript', ''))
                print(f"- {title}: {transcript_len}字")
        
        # 保存结果
        output_file = 'transcript_result.json'
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print(f"结果已保存到: {output_file}")
        
        return True
        
    except requests.exceptions.Timeout:
        print(f"请求超时（超过300秒）")
        return False
        
    except requests.exceptions.ConnectionError:
        print(f"连接失败，请确保服务已启动在 http://localhost:2024")
        print("启动步骤:")
        print("1. cd yunzhishi-agent-service")
        print("2. 配置 .env 文件")
        print("3. langgraph dev --host 0.0.0.0 --port 2024")
        return False
        
    except Exception as e:
        print(f"请求失败: {str(e)}")
        return False

def check_service_status():
    """检查服务状态"""
    print("检查原始项目服务状态...")
    
    try:
        # 尝试访问健康检查端点（如果有的话）
        response = requests.get("http://localhost:2024/health", timeout=5)
        if response.status_code == 200:
            print("服务运行正常")
            return True
    except:
        pass
    
    try:
        # 尝试访问根路径
        response = requests.get("http://localhost:2024/", timeout=5)
        if response.status_code in [200, 404]:  # 404也表示服务在运行
            print("服务运行正常")
            return True
    except:
        pass
    
    print("服务未运行或无法访问")
    print("启动步骤:")
    print("1. cd yunzhishi-agent-service")
    print("2. 配置 .env 文件")
    print("3. langgraph dev --host 0.0.0.0 --port 2024")
    return False

def main():
    """主函数"""
    print("原始项目测试工具")
    print("=" * 60)
    
    # 检查服务状态
    if not check_service_status():
        return 1
    
    # 运行测试
    success = test_transcript_api()
    
    if success:
        print("\n测试完成！可以使用这些结果与MCP版本进行对比。")
        return 0
    else:
        print("\n测试失败，请检查服务状态和配置。")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
