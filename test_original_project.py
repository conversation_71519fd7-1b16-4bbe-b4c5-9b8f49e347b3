#!/usr/bin/env python3
"""原始项目测试脚本"""
import requests
import json
import time
import sys
from pathlib import Path

def test_original_api():
    """测试原始项目API"""
    url = "http://localhost:2024/transcript"
    
    # 测试数据
    test_cases = [
        {
            "name": "基础测试用例",
            "data": {
                "messages": [
                    {
                        "type": "human",
                        "content": {
                            "course_basic_info": "小学三年级数学，面积单元",
                            "teaching_info": "让学生理解面积概念，学会计算长方形面积",
                            "personal_requirements": "注重动手操作，多用生活实例"
                        }
                    }
                ]
            }
        },
        {
            "name": "复杂教学内容测试",
            "data": {
                "messages": [
                    {
                        "type": "human",
                        "content": {
                            "course_basic_info": "初中政治八年级上册《坚持国家利益至上》",
                            "teaching_info": "一、课程导入：家国情怀的传承与思辨- 展示陆之方一家三代戍边案例，提问"为何两代人坚守边疆？"，引导学生思考个人选择与国家利益的关系。二、案例分析：国家利益的维护路径与思辨- 呈现网络泄密调研数据，开展"军迷论坛发言"情景模拟。三、法律解析与责任担当：思辨与行动- 解析《保密法》《国防法》核心条款。四、课程总结：知行合一的爱国实践与思辨- 构建"国家利益金字塔"思维导图。",
                            "personal_requirements": "注重思辨性教学，增加学生互动"
                        }
                    }
                ]
            }
        }
    ]
    
    print("🚀 测试原始项目API...")
    print("=" * 60)
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 测试用例 {i}: {test_case['name']}")
        print("-" * 40)
        
        start_time = time.time()
        
        try:
            print("   📤 发送请求...")
            response = requests.post(url, json=test_case['data'], timeout=300)
            response.raise_for_status()
            
            result = response.json()
            end_time = time.time()
            
            # 分析结果
            final_transcript = result.get('final_transcript', '')
            teaching_processes = result.get('teaching_processes', {})
            
            print(f"   ✅ 请求成功，耗时: {end_time - start_time:.2f}秒")
            print(f"   📊 结果统计:")
            print(f"      - 最终逐字稿长度: {len(final_transcript)}字")
            print(f"      - 教学环节数量: {len(teaching_processes)}")
            
            # 显示教学环节
            if teaching_processes:
                print(f"   📋 教学环节:")
                for process_id, process_info in teaching_processes.items():
                    title = process_info.get('process_title', '未知')
                    transcript_len = len(process_info.get('transcript', ''))
                    print(f"      - {title}: {transcript_len}字")
            
            # 保存结果
            output_file = f'original_result_case_{i}.json'
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"   💾 结果已保存到: {output_file}")
            
            # 保存简化版本用于对比
            simplified_result = {
                "test_case": test_case['name'],
                "generation_time": end_time - start_time,
                "final_transcript_length": len(final_transcript),
                "teaching_sections_count": len(teaching_processes),
                "teaching_sections": [
                    {
                        "title": info.get('process_title', ''),
                        "content_length": len(info.get('content', '')),
                        "transcript_length": len(info.get('transcript', ''))
                    }
                    for info in teaching_processes.values()
                ],
                "final_transcript_preview": final_transcript[:500] + "..." if len(final_transcript) > 500 else final_transcript
            }
            
            results.append(simplified_result)
            
        except requests.exceptions.Timeout:
            print(f"   ⏰ 请求超时（超过300秒）")
            results.append({
                "test_case": test_case['name'],
                "error": "timeout",
                "generation_time": 300
            })
            
        except requests.exceptions.ConnectionError:
            print(f"   🔌 连接失败，请确保服务已启动在 http://localhost:2024")
            results.append({
                "test_case": test_case['name'],
                "error": "connection_failed"
            })
            
        except Exception as e:
            print(f"   ❌ 请求失败: {str(e)}")
            results.append({
                "test_case": test_case['name'],
                "error": str(e)
            })
    
    # 保存汇总结果
    summary_file = 'original_test_summary.json'
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump({
            "test_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "total_cases": len(test_cases),
            "results": results
        }, f, ensure_ascii=False, indent=2)
    
    print(f"\n📊 测试完成，汇总结果已保存到: {summary_file}")
    
    # 显示汇总统计
    successful_tests = [r for r in results if "error" not in r]
    print(f"\n📈 测试汇总:")
    print(f"   - 总测试数: {len(test_cases)}")
    print(f"   - 成功数: {len(successful_tests)}")
    print(f"   - 成功率: {len(successful_tests)/len(test_cases)*100:.1f}%")
    
    if successful_tests:
        avg_time = sum(r['generation_time'] for r in successful_tests) / len(successful_tests)
        avg_length = sum(r['final_transcript_length'] for r in successful_tests) / len(successful_tests)
        print(f"   - 平均生成时间: {avg_time:.2f}秒")
        print(f"   - 平均逐字稿长度: {avg_length:.0f}字")
    
    return results

def check_service_status():
    """检查服务状态"""
    print("🔍 检查原始项目服务状态...")
    
    try:
        # 尝试访问健康检查端点（如果有的话）
        response = requests.get("http://localhost:2024/health", timeout=5)
        if response.status_code == 200:
            print("✅ 服务运行正常")
            return True
    except:
        pass
    
    try:
        # 尝试访问根路径
        response = requests.get("http://localhost:2024/", timeout=5)
        if response.status_code in [200, 404]:  # 404也表示服务在运行
            print("✅ 服务运行正常")
            return True
    except:
        pass
    
    print("❌ 服务未运行或无法访问")
    print("📝 启动步骤:")
    print("   1. cd yunzhishi-agent-service")
    print("   2. 配置 .env 文件")
    print("   3. langgraph dev --host 0.0.0.0 --port 2024")
    return False

def main():
    """主函数"""
    print("🚀 原始项目测试工具")
    print("=" * 60)
    
    # 检查服务状态
    if not check_service_status():
        return 1
    
    # 运行测试
    try:
        results = test_original_api()
        
        # 检查是否有成功的测试
        successful_tests = [r for r in results if "error" not in r]
        if successful_tests:
            print("\n🎉 测试完成！可以使用这些结果与MCP版本进行对比。")
            print("\n📝 下一步:")
            print("   1. 运行 MCP 项目测试: python test_mcp_project.py")
            print("   2. 运行对比分析: python compare_results.py")
            return 0
        else:
            print("\n⚠️ 所有测试都失败了，请检查服务状态和配置。")
            return 1
            
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        return 1
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {str(e)}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
