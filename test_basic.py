#!/usr/bin/env python3
"""最基础的测试脚本"""
import requests
import json

def test_basic():
    url = "http://localhost:2024/transcript"
    
    # 最简单的测试数据
    data = {
        "messages": [
            {
                "type": "human",
                "content": {
                    "course_basic_info": "小学三年级数学，面积单元",
                    "teaching_info": "让学生理解面积概念，学会计算长方形面积",
                    "personal_requirements": "注重动手操作，多用生活实例"
                }
            }
        ]
    }
    
    print("发送请求到原始项目...")
    
    try:
        response = requests.post(url, json=data, timeout=300)
        
        if response.status_code == 200:
            result = response.json()
            print("成功!")
            print(f"逐字稿长度: {len(result.get('final_transcript', ''))}字")
            print(f"教学环节数量: {len(result.get('teaching_processes', {}))}")
            
            # 保存结果
            with open('result.json', 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print("结果已保存到 result.json")
            
        else:
            print(f"失败: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    test_basic()
