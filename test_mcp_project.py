#!/usr/bin/env python3
"""MCP项目测试脚本"""
import asyncio
import json
import time
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
mcp_dir = current_dir / "transcript_generator_mcp"
sys.path.insert(0, str(mcp_dir))

async def test_mcp_tools():
    """测试MCP工具"""
    
    # 测试用例（与原始项目相同）
    test_cases = [
        {
            "name": "基础测试用例",
            "course_basic_info": "小学三年级数学，面积单元",
            "teaching_info": "让学生理解面积概念，学会计算长方形面积",
            "personal_requirements": "注重动手操作，多用生活实例"
        },
        {
            "name": "复杂教学内容测试",
            "course_basic_info": "初中政治八年级上册《坚持国家利益至上》",
            "teaching_info": "一、课程导入：家国情怀的传承与思辨- 展示陆之方一家三代戍边案例，提问"为何两代人坚守边疆？"，引导学生思考个人选择与国家利益的关系。二、案例分析：国家利益的维护路径与思辨- 呈现网络泄密调研数据，开展"军迷论坛发言"情景模拟。三、法律解析与责任担当：思辨与行动- 解析《保密法》《国防法》核心条款。四、课程总结：知行合一的爱国实践与思辨- 构建"国家利益金字塔"思维导图。",
            "personal_requirements": "注重思辨性教学，增加学生互动"
        }
    ]
    
    print("🚀 测试MCP项目工具...")
    print("=" * 60)
    
    try:
        # 导入MCP工具
        from main import generate_transcript, initialize_service, health_check
        
        # 初始化服务
        print("🔧 初始化MCP服务...")
        initialize_service()
        print("✅ 服务初始化成功")
        
        # 模拟MCP上下文
        class MockContext:
            def __init__(self):
                self.messages = []
                self.progress_updates = []
            
            async def info(self, message):
                self.messages.append(message)
                if isinstance(message, dict):
                    status = message.get("status", "")
                    progress = message.get("progress")
                    
                    if status == "started":
                        print("   🎬 开始生成...")
                    elif status == "flow_generation_completed":
                        print(f"   📋 教学流程生成完成")
                    elif status == "section_generation_started":
                        section_title = message.get("section_title", "")
                        print(f"   📝 开始生成环节: {section_title}")
                    elif status == "section_generation_completed":
                        section_title = message.get("section_title", "")
                        print(f"   ✅ 环节生成完成: {section_title}")
                    elif status == "completed":
                        print(f"   🎉 全部生成完成")
                    elif progress is not None:
                        self.progress_updates.append(progress)
                        if len(self.progress_updates) % 5 == 0:  # 每5次更新显示一次
                            print(f"   📊 进度: {progress:.0f}%")
            
            async def error(self, error):
                print(f"   ❌ 错误: {error}")
        
        results = []
        
        # 运行测试用例
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📝 测试用例 {i}: {test_case['name']}")
            print("-" * 40)
            
            ctx = MockContext()
            start_time = time.time()
            
            try:
                result_json = await generate_transcript(
                    course_basic_info=test_case['course_basic_info'],
                    teaching_info=test_case['teaching_info'],
                    ctx=ctx,
                    personal_requirements=test_case['personal_requirements']
                )
                end_time = time.time()
                
                result = json.loads(result_json)
                
                if result["success"]:
                    data = result["data"]
                    print(f"   ✅ 生成成功，耗时: {end_time - start_time:.2f}秒")
                    print(f"   📊 结果统计:")
                    print(f"      - 最终逐字稿长度: {len(data['final_transcript'])}字")
                    print(f"      - 教学环节数量: {data['total_sections']}")
                    print(f"      - 预计时长: {data['estimated_duration']}分钟")
                    print(f"      - 实时消息数: {len(ctx.messages)}")
                    print(f"      - 进度更新数: {len(ctx.progress_updates)}")
                    
                    # 显示教学环节
                    if data['teaching_sections']:
                        print(f"   📋 教学环节:")
                        for section in data['teaching_sections']:
                            title = section.get('title', '未知')
                            transcript_len = len(section.get('transcript', ''))
                            duration = section.get('duration_minutes', 0)
                            print(f"      - {title}: {transcript_len}字, {duration}分钟")
                    
                    # 保存结果
                    output_file = f'mcp_result_case_{i}.json'
                    with open(output_file, 'w', encoding='utf-8') as f:
                        json.dump(result, f, ensure_ascii=False, indent=2)
                    print(f"   💾 结果已保存到: {output_file}")
                    
                    # 保存简化版本用于对比
                    simplified_result = {
                        "test_case": test_case['name'],
                        "generation_time": end_time - start_time,
                        "final_transcript_length": len(data['final_transcript']),
                        "teaching_sections_count": data['total_sections'],
                        "estimated_duration": data['estimated_duration'],
                        "teaching_sections": [
                            {
                                "title": section.get('title', ''),
                                "content_length": len(section.get('content', '')),
                                "transcript_length": len(section.get('transcript', '')),
                                "duration_minutes": section.get('duration_minutes', 0)
                            }
                            for section in data['teaching_sections']
                        ],
                        "final_transcript_preview": data['final_transcript'][:500] + "..." if len(data['final_transcript']) > 500 else data['final_transcript'],
                        "real_time_messages": len(ctx.messages),
                        "progress_updates": len(ctx.progress_updates)
                    }
                    
                    results.append(simplified_result)
                    
                else:
                    print(f"   ❌ 生成失败: {result.get('error', 'Unknown error')}")
                    results.append({
                        "test_case": test_case['name'],
                        "error": result.get('error', 'Unknown error'),
                        "generation_time": end_time - start_time
                    })
                    
            except Exception as e:
                end_time = time.time()
                print(f"   ❌ 测试异常: {str(e)}")
                results.append({
                    "test_case": test_case['name'],
                    "error": str(e),
                    "generation_time": end_time - start_time
                })
        
        # 保存汇总结果
        summary_file = 'mcp_test_summary.json'
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump({
                "test_time": time.strftime("%Y-%m-%d %H:%M:%S"),
                "total_cases": len(test_cases),
                "results": results
            }, f, ensure_ascii=False, indent=2)
        
        print(f"\n📊 测试完成，汇总结果已保存到: {summary_file}")
        
        # 显示汇总统计
        successful_tests = [r for r in results if "error" not in r]
        print(f"\n📈 测试汇总:")
        print(f"   - 总测试数: {len(test_cases)}")
        print(f"   - 成功数: {len(successful_tests)}")
        print(f"   - 成功率: {len(successful_tests)/len(test_cases)*100:.1f}%")
        
        if successful_tests:
            avg_time = sum(r['generation_time'] for r in successful_tests) / len(successful_tests)
            avg_length = sum(r['final_transcript_length'] for r in successful_tests) / len(successful_tests)
            avg_sections = sum(r['teaching_sections_count'] for r in successful_tests) / len(successful_tests)
            print(f"   - 平均生成时间: {avg_time:.2f}秒")
            print(f"   - 平均逐字稿长度: {avg_length:.0f}字")
            print(f"   - 平均教学环节数: {avg_sections:.1f}个")
        
        return results
        
    except ImportError as e:
        print(f"❌ 导入失败: {str(e)}")
        print("📝 请确保在 transcript_generator_mcp 目录下运行此脚本")
        return None
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

async def test_health_check():
    """测试健康检查"""
    print("\n🏥 测试健康检查...")
    
    try:
        from main import health_check
        
        class MockContext:
            async def info(self, message): pass
            async def error(self, error): pass
        
        result_json = await health_check(ctx=MockContext())
        result = json.loads(result_json)
        
        if result["status"] == "healthy":
            print("✅ 健康检查通过")
            config = result.get('config', {})
            print(f"   - AI提供商: {config.get('ai_provider', 'Unknown')}")
            print(f"   - 模型名称: {config.get('model_name', 'Unknown')}")
            print(f"   - 测试模式: {config.get('test_mode', 'Unknown')}")
            return True
        else:
            print(f"❌ 健康检查失败: {result}")
            return False
            
    except Exception as e:
        print(f"❌ 健康检查异常: {str(e)}")
        return False

async def main():
    """主函数"""
    print("🚀 MCP项目测试工具")
    print("=" * 60)
    
    # 检查健康状态
    health_ok = await test_health_check()
    if not health_ok:
        print("\n⚠️ 健康检查失败，但继续进行功能测试...")
    
    # 运行功能测试
    try:
        results = await test_mcp_tools()
        
        if results is None:
            return 1
        
        # 检查是否有成功的测试
        successful_tests = [r for r in results if "error" not in r]
        if successful_tests:
            print("\n🎉 测试完成！可以使用这些结果与原始版本进行对比。")
            print("\n📝 下一步:")
            print("   1. 运行对比分析: python compare_results.py")
            print("   2. 查看详细结果文件: mcp_result_case_*.json")
            return 0
        else:
            print("\n⚠️ 所有测试都失败了，请检查配置和环境。")
            return 1
            
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        return 1
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {str(e)}")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
